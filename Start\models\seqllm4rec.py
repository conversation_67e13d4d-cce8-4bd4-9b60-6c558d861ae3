"""
基于LLM的推荐模块（models/seqllm4rec.py）

核心思路：
- 使用预训练的大语言模型（如 Llama）作为编码器
- 通过在Prompt中注入特殊token占位符：[UserOut]、[ItemOut]、[HistoryEmb]
- 利用替换机制把历史物品的稀疏ID嵌入（由RecSys投影后）直接替换到输入嵌入流中
- 从最后一层隐藏状态中抽取这些占位符对应的位置向量，作为用户/物品表示
- 通过轻量的预测头（pred_user/pred_item）和损失函数进行训练与对齐

文件包含：
- llm4rec 类：封装LLM、特殊token处理、表示抽取与损失计算

技术特点：
- 特殊token机制：通过占位符实现向量注入
- 嵌入替换：将CF-SRec嵌入无缝融入LLM输入
- 多头预测：用户表示头、物品表示头、CF对齐头
- 多重损失：推荐损失、对齐损失、均匀性正则
"""

# 设置Hugging Face镜像
import os
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoTokenizer, OPTForCausalLM, AutoModelForCausalLM
from peft import (
    prepare_model_for_kbit_training,  # 用于低精度训练的模型准备
)
class llm4rec(nn.Module):
    """
    以大语言模型为核心的推荐表示学习模块。

    主要职责：
    - 加载指定的预训练LLM（如 Llama 系列）与对应分词器
    - 注册与扩展项目需要的特殊token（[UserRep]/[HistoryEmb]/[UserOut]/[ItemOut]）
    - 冻结大部分LLM参数，仅训练轻量级头部或部分嵌入（可选）
    - 提供若干工具函数：
      * replace_out_token_all / replace_out_token_all_infer：将文本中的占位符替换为向量
      * get_embeddings：定位并提取指定token位置
      * rec_loss / info_nce / uniformity：训练损失计算
    - 前向流程（train_mode0）：
      1) 用历史交互嵌入替换 [HistoryEmb]，从 LLM hidden states 抽取 [UserOut] 表示
      2) 用候选物品嵌入替换 [HistoryEmb]，从 LLM hidden states 抽取 [ItemOut] 表示
      3) 通过预测头映射到统一维度，计算推荐与对齐损失
    """
    def __init__(
        self,
        device,
        llm_model="",
        max_output_txt_len=256,
        args=None
    ):
        """
        初始化 llm4rec 模块

        Args:
            device (str): 计算设备，如 'cuda:0' 或 'cpu'
            llm_model (str): LLM模型名称，支持 'llama' 和 'llama-3b'
            max_output_txt_len (int): 最大输出文本长度，默认256
            args: 全局配置对象，包含训练策略和硬件配置

        初始化流程：
        1. 加载指定的预训练LLM模型和分词器
        2. 扩展词汇表以支持特殊token
        3. 配置参数冻结策略
        4. 初始化预测头和对齐模块
        """
        super().__init__()
        self.device = device
        self.bce_criterion = torch.nn.BCEWithLogitsLoss()  # 二元交叉熵损失（保留用于扩展）
        self.args = args

        # 步骤1: 选择并配置LLM模型
        if llm_model == 'llama':
            model_id = "meta-llama/Meta-Llama-3-8B-Instruct"
        elif llm_model == 'llama-3b':
            model_id = "meta-llama/Llama-3.2-3B-Instruct"
        else:
            raise Exception(f'{llm_model} is not supported')
        print()
        print("=========")

        # 步骤2: 加载LLM模型（根据硬件配置选择精度策略）
        if self.args.nn_parameter:
            # 神经网络处理器模式：使用fp16精度，无量化
            self.llm_model = AutoModelForCausalLM.from_pretrained(
                model_id,
                device_map=self.device,
                torch_dtype=torch.float16
            )
        else:
            # 标准GPU模式：使用8-bit量化节省显存
            self.llm_model = AutoModelForCausalLM.from_pretrained(
                model_id,
                device_map=self.device,
                torch_dtype=torch.float16,
                load_in_8bit=True,
            )

        # 步骤3: 加载对应的分词器
        self.llm_tokenizer = AutoTokenizer.from_pretrained(model_id, use_fast=False)

        # 步骤4: 扩展分词器的特殊token
        # 添加标准的特殊token
        self.llm_tokenizer.add_special_tokens({'pad_token': '[PAD]'})    # 填充token
        self.llm_tokenizer.add_special_tokens({'bos_token': '</s>'})     # 序列开始token
        self.llm_tokenizer.add_special_tokens({'eos_token': '</s>'})     # 序列结束token
        self.llm_tokenizer.add_special_tokens({'unk_token': '</s>'})     # 未知token

        # 添加项目专用的特殊token
        # [UserRep]: 用户表示token（保留用于扩展）
        # [HistoryEmb]: 历史物品嵌入占位符，会被实际的物品嵌入替换
        # [UserOut]: 用户输出位置标记，LLM在此位置生成用户表示
        # [ItemOut]: 物品输出位置标记，LLM在此位置生成物品表示
        self.llm_tokenizer.add_special_tokens({
            'additional_special_tokens': ['[UserRep]', '[HistoryEmb]', '[UserOut]', '[ItemOut]']
        })
        self.llm_tokenizer.add_special_tokens({'cls_token': "[CLS]"})     # 分类token

        # 步骤5: 调整模型以适应扩展的词汇表
        self.llm_model.resize_token_embeddings(len(self.llm_tokenizer))
        self.llm_model = prepare_model_for_kbit_training(self.llm_model)

        # 步骤6: 配置参数冻结策略
        for name, param in self.llm_model.named_parameters():
            if args.token:
                # 策略A: 仅训练词嵌入层（token embedding）
                if 'token' in name:
                    param.requires_grad = True
                else:
                    param.requires_grad = False
            else:
                # 策略B: 冻结所有LLM参数，使用可学习的CLS向量
                param.requires_grad = False

        # 步骤7: 初始化可学习的占位符向量（当不训练词嵌入时）
        if not args.token:
            if args.nn_parameter:
                # 神经网络处理器模式：使用Parameter直接存储
                self.CLS = nn.Parameter(torch.normal(0, 1, size=(1, self.llm_model.config.hidden_size))).to(device)
                self.CLS_item = nn.Parameter(torch.normal(0, 1, size=(1, self.llm_model.config.hidden_size))).to(device)
            else:
                # 标准模式：使用Embedding层存储
                self.CLS = nn.Embedding(1, self.llm_model.config.hidden_size).to(device)
                # 使用LLM词嵌入的统计特性初始化CLS向量
                nn.init.normal_(
                    self.CLS.weight,
                    mean=self.llm_model.model.embed_tokens.weight.mean(),
                    std=self.llm_model.model.embed_tokens.weight.std()
                )

                self.CLS_item = nn.Embedding(1, self.llm_model.config.hidden_size).to(device)
                nn.init.normal_(
                    self.CLS_item.weight,
                    mean=self.llm_model.model.embed_tokens.weight.mean(),
                    std=self.llm_model.model.embed_tokens.weight.std()
                )

        # 步骤8: 初始化预测头网络
        # 用户表示预测头：将LLM隐藏状态映射到统一的用户表示空间
        # 架构：LLM_hidden_size -> 2048 -> LayerNorm -> LeakyReLU -> 128
        self.pred_user = nn.Sequential(
                nn.Linear(self.llm_model.config.hidden_size, 2048),  # 第一层：维度扩展
                nn.LayerNorm(2048),                                  # 层归一化
                nn.LeakyReLU(),                                      # 激活函数
                nn.Linear(2048, 128)                                 # 第二层：映射到最终维度
            )
        # 使用Xavier初始化提高训练稳定性
        nn.init.xavier_normal_(self.pred_user[0].weight)
        nn.init.xavier_normal_(self.pred_user[3].weight)

        # 物品表示预测头：将LLM隐藏状态映射到统一的物品表示空间
        # 架构与用户预测头相同，但参数独立
        self.pred_item = nn.Sequential(
                nn.Linear(self.llm_model.config.hidden_size, 2048),
                nn.LayerNorm(2048),
                nn.LeakyReLU(),
                nn.Linear(2048, 128)
            )
        nn.init.xavier_normal_(self.pred_item[0].weight)
        nn.init.xavier_normal_(self.pred_item[3].weight)

        # CF用户表示对齐头：将CF-SRec的用户表示(64维)映射到统一空间(128维)
        # 用于知识蒸馏，使LLM学习CF模型的用户表示
        self.pred_user_CF2 = nn.Sequential(
                nn.Linear(64, 128),      # CF维度(64) -> 统一维度(128)
                nn.LayerNorm(128),       # 层归一化
                nn.GELU(),               # GELU激活函数
                nn.Linear(128, 128)      # 进一步变换
            )
        nn.init.xavier_normal_(self.pred_user_CF2[0].weight)
        nn.init.xavier_normal_(self.pred_user_CF2[3].weight)

        # CF到潜在空间的映射头（保留用于扩展，当前未使用）
        self.cf_to_latent2 = nn.Sequential(
                nn.Linear(64, 128),
                nn.LayerNorm(128),
                nn.GELU(),
                nn.Linear(128, 128)
            )
        nn.init.xavier_normal_(self.cf_to_latent2[0].weight)
        nn.init.xavier_normal_(self.cf_to_latent2[3].weight)

        # 步骤9: 初始化损失函数和配置参数
        self.mse = nn.MSELoss()  # 均方误差损失，用于表示对齐
        self.max_output_txt_len = max_output_txt_len  # 最大输出文本长度

    def info_nce_loss_batch(self, anchor, log_emb, temperature=0.07):
        """
        批量InfoNCE对比损失计算

        InfoNCE是一种对比学习损失，通过最大化正样本相似度、最小化负样本相似度来学习表示。
        在这里用于对齐LLM生成的用户表示与CF-SRec的用户表示。

        Args:
            anchor (torch.Tensor): 锚点表示（LLM用户表示），形状 [batch_size, dim]
            log_emb (torch.Tensor): 目标表示（CF用户表示），形状 [batch_size, dim]
            temperature (float): 温度参数，控制分布的尖锐程度，默认0.07

        Returns:
            torch.Tensor: InfoNCE损失值

        计算原理：
        1. 对表示进行L2归一化
        2. 计算相似度矩阵（内积/温度）
        3. 对角线元素为正样本，其余为负样本
        4. 使用交叉熵损失优化
        """
        batch_size = anchor.shape[0]

        # L2归一化：将向量归一化到单位球面上
        anchor = F.normalize(anchor, p=2, dim=1)
        log_emb = F.normalize(log_emb, p=2, dim=1)

        # 计算相似度矩阵：anchor与log_emb之间的内积相似度
        similarity_matrix = torch.matmul(anchor, log_emb.T) / temperature

        # 创建对角线掩码：对角线位置为正样本，其余为负样本
        mask = torch.eye(batch_size, device=anchor.device).bool()

        # 提取正样本相似度（对角线元素）
        pos_sim = similarity_matrix[mask].view(batch_size, 1)
        # 提取负样本相似度（非对角线元素）
        neg_sim = similarity_matrix[~mask].view(batch_size, -1)

        # 拼接正负样本相似度：正样本在第一列
        logits = torch.cat([pos_sim, neg_sim], dim=1)

        # 标签全为0，表示正样本在第一个位置
        labels = torch.zeros(batch_size, dtype=torch.long, device=anchor.device)

        # 计算交叉熵损失
        loss = F.cross_entropy(logits, labels)

        return loss

    def rec_loss(self, anchor, items):
        """
        推荐损失：将正样本置于第一位，使用交叉熵进行排序学习

        这个损失函数用于训练模型学习推荐排序，确保用户表示与正样本物品表示
        的相似度高于与负样本物品表示的相似度。

        Args:
            anchor (torch.Tensor): 用户表示，形状 [batch_size, dim]
            items (torch.Tensor): 候选物品表示，形状 [batch_size, num_candidates, dim]
                                 其中第一个物品是正样本，其余是负样本

        Returns:
            torch.Tensor: 推荐损失值

        计算过程：
        1. 计算用户表示与所有候选物品的相似度
        2. 使用交叉熵损失，标签为0（正样本在第一位）
        """
        # 计算用户表示与候选物品表示的相似度得分
        # items.view: 重塑为 [batch_size, num_candidates, dim]
        # anchor.unsqueeze(2): 扩展为 [batch_size, dim, 1]
        # bmm: 批量矩阵乘法，得到 [batch_size, num_candidates, 1]
        # squeeze(2): 压缩最后一维，得到 [batch_size, num_candidates]
        logits = torch.bmm(items.view(anchor.shape[0], -1, anchor.shape[1]), anchor.unsqueeze(2)).squeeze(2)

        # 标签全为0，表示正样本在第一个位置
        labels = torch.zeros(logits.size(0), dtype=torch.long).to(logits.device)

        # 计算交叉熵损失
        loss = F.cross_entropy(logits, labels)

        return loss

    def uniformity(self, x, p=2):
        """
        Uniformity正则化损失：鼓励表征在空间中均匀分布

        这个正则化项防止表征塌缩到空间中的某个小区域，
        鼓励学到的表征能够充分利用整个表征空间。

        Args:
            x (torch.Tensor): 输入表征，形状 [batch_size, dim]
            p (int): 距离的p范数，默认为2（欧几里得距离）

        Returns:
            torch.Tensor: Uniformity损失值

        计算公式：
        uniformity = mean(exp(-p * ||xi - xj||_p^2))
        其中xi, xj是批次中的不同样本
        """
        return torch.pdist(x, p=p).pow(2).mul(-p).exp().mean()

    def replace_out_token_all(self, llm_tokens, inputs_embeds, token=[], embs=None):
        """
        训练场景的特殊token替换函数

        这是LLM-SRec的核心机制：将文本中的特殊token占位符替换为实际的向量表示。
        通过这种方式，可以将CF-SRec的物品嵌入无缝注入到LLM的输入中。

        Args:
            llm_tokens (dict): 分词后的token字典，包含 'input_ids' 等
            inputs_embeds (torch.Tensor): LLM的输入嵌入，形状 [batch_size, seq_len, hidden_size]
            token (list): 需要替换的特殊token列表，如 ['[UserOut]', '[HistoryEmb]']
            embs (dict): 替换用的嵌入字典，键为token名，值为对应的嵌入张量

        Returns:
            torch.Tensor: 替换后的输入嵌入

        替换策略：
        - [HistoryEmb]: 替换为历史物品的嵌入序列（支持多个物品）
        - [UserRep]: 替换为用户表示嵌入
        - [UserOut]/[ItemOut]: 替换为可学习的CLS向量（当不训练token时）

        工作原理：
        1. 遍历每个需要替换的token
        2. 找到该token在输入序列中的所有位置
        3. 根据token类型选择相应的替换策略
        4. 逐位置替换，保持序列长度不变
        """
        for t in token:
            # 获取当前token的ID
            token_id = self.llm_tokenizer(t, return_tensors="pt", add_special_tokens=False).input_ids.item()
            vectors = []

            # 遍历批次中的每个样本
            for inx in range(len(llm_tokens["input_ids"])):
                # 找到当前token在序列中的所有位置
                idx_tensor = (llm_tokens["input_ids"][inx] == token_id).nonzero().view(-1)
                user_vector = inputs_embeds[inx]  # 当前样本的嵌入序列

                if 'Emb' in t:
                    # 处理嵌入类token（如[HistoryEmb]）
                    # 用提供的嵌入序列替换对应位置
                    ee = embs[t][inx]  # 获取当前样本的嵌入序列
                    for idx, item_emb in zip(idx_tensor, ee):
                        # 逐个替换：保留前面部分 + 新嵌入 + 保留后面部分
                        user_vector = torch.cat((user_vector[:idx], item_emb.unsqueeze(0), user_vector[idx+1:]), dim=0)

                elif 'Rep' in t:
                    # 处理表示类token（如[UserRep]）
                    # 用单个表示向量替换对应位置
                    for idx in idx_tensor:
                        user_emb = embs[t][inx]
                        user_vector = torch.cat((user_vector[:idx], user_emb.unsqueeze(0), user_vector[idx+1:]), dim=0)

                else:
                    # 处理输出类token（如[UserOut], [ItemOut]）
                    # 当不训练token时，用可学习的CLS向量替换
                    if not self.args.token:
                        for idx in idx_tensor:
                            if 'UserOut' in t:
                                # 替换为用户CLS向量
                                if self.args.nn_parameter:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS[torch.tensor([0]).to(self.device)], user_vector[idx+1:]), dim=0)
                                else:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS(torch.tensor([0]).to(self.device)), user_vector[idx+1:]), dim=0)
                            elif 'ItemOut' in t:
                                # 替换为物品CLS向量
                                if self.args.nn_parameter:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS_item[torch.tensor([0]).to(self.device)], user_vector[idx+1:]), dim=0)
                                else:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS_item(torch.tensor([0]).to(self.device)), user_vector[idx+1:]), dim=0)

                vectors.append(user_vector.unsqueeze(0))

            # 将所有样本的替换结果拼接回批次
            inputs_embeds = torch.cat(vectors)
        return inputs_embeds
    
    def replace_out_token_all_infer(self, llm_tokens, inputs_embeds, token=[], embs=None, user_act=False, item_act=False):
        """
        推理场景的特殊token替换函数

        与 replace_out_token_all 的区别：
        - replace_out_token_all: 训练场景，支持序列化的嵌入替换
        - replace_out_token_all_infer: 推理场景，简化的嵌入替换

        主要差异：
        1. [HistoryEmb] 只替换一次（每条样本一个聚合向量）
        2. 优化了内存使用和计算效率
        3. 保留了未使用的参数以保持接口兼容性

        Args:
            llm_tokens (dict): 分词后的token字典
            inputs_embeds (torch.Tensor): LLM的输入嵌入
            token (list): 需要替换的特殊token列表
            embs (dict): 替换用的嵌入字典
            user_act (bool): 用户激活标志（保留用于扩展，当前未使用）
            item_act (bool): 物品激活标志（保留用于扩展，当前未使用）

        Returns:
            torch.Tensor: 替换后的输入嵌入
        """
        for t in token:
            # 获取当前token的ID
            token_id = self.llm_tokenizer(t, return_tensors="pt", add_special_tokens=False).input_ids.item()
            vectors = []

            # 遍历批次中的每个样本
            for inx in range(len(llm_tokens["input_ids"])):
                # 找到当前token在序列中的所有位置
                idx_tensor = (llm_tokens["input_ids"][inx] == token_id).nonzero().view(-1)
                user_vector = inputs_embeds[inx]

                if 'Emb' in t:
                    # 处理嵌入类token（推理模式的简化版本）
                    # 将单个嵌入包装成列表，保持与训练模式的接口一致
                    ee = [embs[t][inx]]
                    # 注释掉的代码：ee = embs[t][inx] （原始实现）
                    for idx, item_emb in zip(idx_tensor, ee):
                        user_vector = torch.cat((user_vector[:idx], item_emb.unsqueeze(0), user_vector[idx+1:]), dim=0)

                elif 'Rep' in t:
                    # 处理表示类token（与训练模式相同）
                    for idx in idx_tensor:
                        user_emb = embs[t][inx]
                        user_vector = torch.cat((user_vector[:idx], user_emb.unsqueeze(0), user_vector[idx+1:]), dim=0)

                else:
                    # 处理输出类token（与训练模式相同）
                    if not self.args.token:
                        for idx in idx_tensor:
                            if 'UserOut' in t:
                                # 替换为用户CLS向量
                                if self.args.nn_parameter:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS[torch.tensor([0]).to(self.device)], user_vector[idx+1:]), dim=0)
                                else:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS(torch.tensor([0]).to(self.device)), user_vector[idx+1:]), dim=0)
                            elif 'ItemOut' in t:
                                # 替换为物品CLS向量
                                if self.args.nn_parameter:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS_item[torch.tensor([0]).to(self.device)], user_vector[idx+1:]), dim=0)
                                else:
                                    user_vector = torch.cat((user_vector[:idx], self.CLS_item(torch.tensor([0]).to(self.device)), user_vector[idx+1:]), dim=0)

                vectors.append(user_vector.unsqueeze(0))

            # 将所有样本的替换结果拼接回批次
            inputs_embeds = torch.cat(vectors)
        return inputs_embeds

    def get_embeddings(self, llm_tokens, token):
        """
        获取指定特殊token在输入序列中的位置索引

        这个函数用于定位特殊token（如[UserOut], [ItemOut]）在tokenized序列中的位置，
        以便后续从LLM的隐藏状态中提取对应位置的表示。

        Args:
            llm_tokens (dict): 分词后的token字典，包含 'input_ids'
            token (str): 要查找的特殊token，如 '[UserOut]'

        Returns:
            list: 每个样本中该token的位置索引列表
                 形如 [[pos1, pos2], [pos3], ...] 其中每个子列表对应一个样本

        使用场景：
        - 在LLM前向传播后，从hidden_states中提取特定位置的表示
        - 支持一个样本中有多个相同token的情况
        """
        token_idx = []
        # 获取token对应的ID
        token_id = self.llm_tokenizer(token, return_tensors="pt", add_special_tokens=False).input_ids.item()

        # 遍历批次中的每个样本
        for inx in range(len(llm_tokens['input_ids'])):
            # 找到该token在当前样本中的所有位置
            idx_tensor = (llm_tokens['input_ids'][inx] == token_id).nonzero().view(-1)
            token_idx.append(idx_tensor)
        return token_idx

    def forward(self, samples, mode=0):
        """
        模型前向传播的统一入口

        根据不同的模式调用相应的训练方法。当前主要支持mode=0的训练模式。

        Args:
            samples (dict): 训练样本字典，包含文本、嵌入等信息
            mode (int): 训练模式
                - 0: train_mode0，标准的LLM-SRec训练模式
                - 1: train_mode1，保留用于扩展（当前未实现）

        Returns:
            根据具体模式返回相应的损失值和指标
        """
        if mode == 0:
            return self.train_mode0(samples)
        elif mode == 1:
            return self.train_mode1(samples)

    def train_mode0(self, samples):
        """
        LLM-SRec的核心训练模式

        这是模型的主要训练方法，实现以下关键流程：
        1. 处理用户历史文本，替换特殊token，生成用户表示
        2. 处理候选物品文本，替换特殊token，生成物品表示
        3. 计算推荐损失（用户-物品匹配）和对齐损失（与CF表示对齐）

        Args:
            samples (dict): 训练样本字典，包含：
                - 'text_input': 用户历史的文本prompt列表
                - 'log_emb': CF-SRec的用户表示（监督信号）
                - 'candidates_pos': 候选物品的文本prompt列表
                - 'interact': 历史物品嵌入列表（用于替换[HistoryEmb]）
                - 'candidate_embs': 候选物品嵌入张量

        Returns:
            Tuple[torch.Tensor, float, float]:
                - loss: 总损失（推荐损失 + 对齐损失）
                - rec_loss.item(): 推荐损失值
                - match_loss.item(): 对齐损失值
        """
        max_input_length = 1024  # LLM输入的最大长度限制
        log_emb = samples['log_emb']  # CF-SRec的用户表示（作为监督信号）

        # 步骤1: 处理用户历史文本
        # 对用户历史prompt进行tokenization
        llm_tokens = self.llm_tokenizer(
            samples['text_input'],
            return_tensors="pt",
            padding="longest",      # 填充到批次中最长的序列
            truncation=True,        # 超长截断
            max_length=max_input_length,
        ).to(self.device)

        # 获取初始的词嵌入
        inputs_embeds = self.llm_model.get_input_embeddings()(llm_tokens['input_ids'])

        # 关键步骤：替换特殊token
        # [UserOut]: 替换为可学习的CLS向量（用于后续提取用户表示）
        # [HistoryEmb]: 替换为历史物品的嵌入序列
        inputs_embeds = self.replace_out_token_all(llm_tokens, inputs_embeds, token = ['[UserOut]', '[HistoryEmb]'], embs= { '[HistoryEmb]':samples['interact']})


        # 步骤2: 处理候选物品文本
        # 对候选物品prompt进行tokenization
        candi_tokens = self.llm_tokenizer(
                samples['candidates_pos'],
                return_tensors="pt",
                padding="longest",
                truncation=True,
                max_length=max_input_length,
            ).to(self.device)

        # 获取候选物品的初始词嵌入
        candi_embeds = self.llm_model.get_input_embeddings()(candi_tokens['input_ids'])

        # 替换候选物品文本中的特殊token
        # [ItemOut]: 替换为可学习的CLS向量（用于后续提取物品表示）
        # [HistoryEmb]: 替换为候选物品的嵌入（这里是物品自身的嵌入）
        candi_embeds = self.replace_out_token_all_infer(candi_tokens, candi_embeds, token = ['[ItemOut]', '[HistoryEmb]'], embs= {'[HistoryEmb]':samples['candidate_embs']})

        # 步骤3: LLM前向传播（使用混合精度加速）
        with torch.amp.autocast('cuda'):

            # 3.1: 处理候选物品，生成物品表示
            candi_outputs = self.llm_model.forward(
                inputs_embeds=candi_embeds,
                output_hidden_states=True  # 需要隐藏状态来提取表示
            )

            # 提取[ItemOut]位置的隐藏状态作为物品表示
            indx = self.get_embeddings(candi_tokens, '[ItemOut]')
            item_outputs = torch.cat([candi_outputs.hidden_states[-1][i,indx[i]].mean(axis=0).unsqueeze(0) for i in range(len(indx))])

            # 3.2: 处理用户历史，生成用户表示
            outputs = self.llm_model.forward(
                inputs_embeds=inputs_embeds,
                output_hidden_states=True
            )

            # 提取[UserOut]位置的隐藏状态作为用户表示
            indx = self.get_embeddings(llm_tokens, '[UserOut]')
            user_outputs = torch.cat([outputs.hidden_states[-1][i,indx[i]].mean(axis=0).unsqueeze(0) for i in range(len(indx))])

        # 步骤4: 通过预测头映射到统一表示空间
        user_outputs = self.pred_user(user_outputs)  # LLM用户表示 -> 统一空间
        item_outputs = self.pred_item(item_outputs)  # LLM物品表示 -> 统一空间

        # 步骤5: 计算推荐损失
        # 使用用户表示与物品表示的匹配程度计算排序损失
        rec_loss = self.rec_loss(user_outputs, item_outputs)

        # 步骤6: 计算对齐损失（知识蒸馏）
        # 将CF-SRec的用户表示映射到统一空间
        log_emb = self.pred_user_CF2(log_emb)

        # L2归一化，确保在单位球面上比较
        user_outputs = F.normalize(user_outputs, p=2, dim=1)  # LLM用户表示
        log_emb = F.normalize(log_emb, p=2, dim=1)            # CF用户表示

        # 计算对齐损失：MSE + Uniformity正则
        match_loss = self.mse(user_outputs, log_emb)  # 表示对齐
        match_loss += (self.uniformity(user_outputs) + self.uniformity(log_emb))  # 防止塌缩

        # 步骤7: 总损失计算
        loss = rec_loss + match_loss

        return loss, rec_loss.item(), match_loss.item()
    
    
    
    